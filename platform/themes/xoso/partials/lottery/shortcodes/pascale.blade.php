@php
    use Plugin\Lottery\Enums\LotteryResultLevelEnum;

    $zero = $numbers->where('level', LotteryResultLevelEnum::ZERO)->first();
    $first = $numbers->where('level', LotteryResultLevelEnum::FIRST)->first();

    dd($zero);
    $zeroNumber = str_split($zero ? $zero->number : '');
    $firstNumber = str_split($first ? $first->number : '');
@endphp

<div class="row">
    <div class="col-12">
        <div class="row rows-col-{{ count($zeroNumber) + count($firstNumber) }}">
            @foreach ($zeroNumber + $firstNumber as $number)
                <div class="col">{{ $number }}</div>
            @endforeach
        </div>
    </div>
</div>
