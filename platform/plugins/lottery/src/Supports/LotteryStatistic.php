<?php

namespace Plugin\Lottery\Supports;

use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Plugin\Lottery\Enums\LotteryRegionEnum;
use Plugin\Lottery\Facades\LotterySupport;
use Plugin\Lottery\Models\LotteryCity;

class LotteryStatistic
{
    public function getLotoResults(Collection $results)
    {
        $lotoNumbers = [];

        $results = $results->whereNotIn('level', ['code', 'special'])->values();

        foreach ($results as $result) {
            $loto = substr(trim($result->number), -2);
            $head = substr($loto, 0, 1);
            $lotoNumbers[$head][] = $loto;
        }

        $data = [];
        for ($i = 0; $i <= 9; $i++) {
            $data[$i] = Arr::get($lotoNumbers, $i, []);
        }

        return $data;
    }

    public function getListLotoFromRequest(): array
    {
        $list = (string) request()->input('list');
        $listLotos = [];
        if ($list) {
            $listLotos = array_filter(explode('-', $list), function ($item) {
                return preg_match('/^\d{2}$/', $item) && intval($item) >= 0 && intval($item) <= 99;
            });
        }

        if (! $listLotos) {
            $listLotos = [];
            while (count($listLotos) < 3) {
                $num = Str::padLeft(rand(0, 99), 2, '0');
                if (! in_array($num, $listLotos)) {
                    $listLotos[] = $num;
                }
            }
        }

        return $listLotos;
    }

    public function getRequestRanges(): array
    {
        return [7, 10, 30, 45, 60, 100, 200, 365];
    }

    public function getRequestYears(): array
    {
        return range(2011, date('Y'));
    }

    public function getRequestRange(): int
    {
        $range = request()->input('range', 30);
        if (! in_array($range, $this->getRequestRanges())) {
            $range = 30;
        }

        return $range;
    }

    public function getRequestYear(): int
    {
        $year ??= request()->input('year') ?? date('Y');
        $years = $this->getRequestYears();

        if (! in_array($year, $years)) {
            $year = date('Y');
        }

        return $year;
    }

    public function getRequestMonth(): int
    {
        $month = request()->input('month');
        if (! in_array($month, range(1, 12))) {
            $month = date('m');
        }

        return $month;
    }

    public function getRequestDate($format = 'd-m-Y'): Carbon
    {
        $yesterday = Carbon::yesterday();
        $date = request()->input('date');

        if ($date && Carbon::hasFormat($date, $format)) {
            $date = Carbon::parse($date);

            if ($date->gt($yesterday)) {
                $date = null;
            }
        } else {
            $date = null;
        }

        if (! $date) {
            $date = $yesterday->copy();
        }

        return $date;
    }

    public function getRequestCity(bool $withFirst = true, ?string $region = null): ?LotteryCity
    {
        $code = request()->input('city_code');

        $city = LotterySupport::getCities()
            ->when($region, fn ($query) => $query->where('region', $region))
            ->where(fn ($city) => $city->subcode == $code)
            ->first();

        if (! $city && $withFirst) {
            $city = LotterySupport::getCities()->first();
        }

        return $city;
    }

    public function getLastTwoDigits($default = null)
    {
        $lastTwoDigits = request()->input('number');

        if (! is_numeric($lastTwoDigits)) {
            $lastTwoDigits = $default ?? rand(0, 99);
        }

        $lastTwoDigits = (int) $lastTwoDigits;

        return substr(str_pad($lastTwoDigits, 2, '0', STR_PAD_LEFT), -2);
    }

    public function getRequestWeekOfYear($default = null)
    {
        $weekOfYear = request()->input('week_of_year');

        if (! in_array($weekOfYear, range(1, 53))) {
            $weekOfYear = $default ?? Carbon::now()->weekOfYear;
        }

        return $weekOfYear;
    }

    public function getRequestDayOfWeek($default = null)
    {
        $dayOfWeek = request()->input('day_of_week');

        if (is_null($dayOfWeek) || ! in_array($dayOfWeek, range(0, 7))) {
            $dayOfWeek = $default ?? Carbon::today()->dayOfWeek;
        }

        return $dayOfWeek;
    }

    public function prizeStructure($region)
    {
        return match ($region) {
            LotteryRegionEnum::MB => [
                'zero' => [
                    'name' => 'Giải Đặc biệt',
                    'amount' => 1000000000,
                ],
                '1st' => [
                    'name' => 'Giải nhất',
                    'digits' => 5,
                    'amount' => 10000000,
                ],
                '2nd' => [
                    'name' => 'Giải Nhì',
                    'amount' => 5000000,
                ],
                '3rd' => [
                    'name' => 'Giải Ba',
                    'amount' => 1000000,
                ],
                '4th' => [
                    'name' => 'Giải Tư',
                    'amount' => 400000,
                ],
                '5th' => [
                    'name' => 'Giải Năm',
                    'amount' => 200000,
                ],
                '6th' => [
                    'name' => 'Giải Sáu',
                    'amount' => 100000,
                ],
                '7th' => [
                    'name' => 'Giải Bảy',
                    'amount' => 40000,
                ],
                'sub-zero' => [
                    'name' => 'Giải Phụ đặc biệt',
                    'amount' => 20000000,
                ],
                'encouragement' => [
                    'name' => 'Giải khuyến khích',
                    'amount' => 40000,
                ],
            ],
            LotteryRegionEnum::MT => [
                'zero' => [
                    'name' => 'Giải Đặc biệt',
                    'amount' => 2000000000,
                ],
                '1st' => [
                    'name' => 'Giải nhất',
                    'digits' => 5,
                    'amount' => 30000000,
                ],
                '2nd' => [
                    'name' => 'Giải Nhì',
                    'amount' => 15000000,
                ],
                '3rd' => [
                    'name' => 'Giải Ba',
                    'amount' => 10000000,
                ],
                '4th' => [
                    'name' => 'Giải Tư',
                    'amount' => 3000000,
                ],
                '5th' => [
                    'name' => 'Giải Năm',
                    'amount' => 1000000,
                ],
                '6th' => [
                    'name' => 'Giải Sáu',
                    'amount' => 400000,
                ],
                '7th' => [
                    'name' => 'Giải Bảy',
                    'amount' => 100000,
                ],
                '8th' => [
                    'name' => 'Giải Tám',
                    'amount' => 100000,
                ],
                'sub-zero' => [
                    'name' => 'Giải Phụ đặc biệt',
                    'amount' => 50000000,
                ],
                'encouragement' => [
                    'name' => 'Giải khuyến khích',
                    'amount' => 6000000,
                ],
            ],
            LotteryRegionEnum::MN => [
                'zero' => [
                    'name' => 'Giải Đặc biệt',
                    'amount' => 2000000000,
                ],
                '1st' => [
                    'name' => 'Giải nhất',
                    'digits' => 5,
                    'amount' => 30000000,
                ],
                '2nd' => [
                    'name' => 'Giải Nhì',
                    'amount' => 15000000,
                ],
                '3rd' => [
                    'name' => 'Giải Ba',
                    'amount' => 10000000,
                ],
                '4th' => [
                    'name' => 'Giải Tư',
                    'amount' => 3000000,
                ],
                '5th' => [
                    'name' => 'Giải Năm',
                    'amount' => 1000000,
                ],
                '6th' => [
                    'name' => 'Giải Sáu',
                    'amount' => 400000,
                ],
                '7th' => [
                    'name' => 'Giải Bảy',
                    'amount' => 100000,
                ],
                '8th' => [
                    'name' => 'Giải Tám',
                    'amount' => 100000,
                ],
                'sub-zero' => [
                    'name' => 'Giải Phụ đặc biệt',
                    'amount' => 50000000,
                ],
                'encouragement' => [
                    'name' => 'Giải khuyến khích',
                    'amount' => 6000000,
                ],
            ],
        };
    }

    public function findPrize($results, $region, ?string $inputNumber = null): array
    {
        $matchedPrizes = [];

        if ($inputNumber) {
            $prizeStructure = $this->prizeStructure($region);

            // Kiểm tra trùng khớp hoàn toàn
            foreach ($results as $result) {
                $level = $result->level;

                $digits = strlen($result->number);
                $inputToCheck = substr($inputNumber, -$digits);

                if ($inputToCheck == $result->number) {
                    $matchedPrizes[$level] = [
                        'id' => $result->id,
                        'result' => $result,
                        'prize' => $prizeStructure[$level]['name'],
                        'number' => $result->number,
                        'matched_digits' => $inputToCheck,
                        'amount' => number_format($prizeStructure[$level]['amount'], 0, ',', '.') . ' đ',
                    ];
                }
            }

            // Giải Phụ đặt biệt là giải dành cho các vé trúng 5 số cuối (chỉ sai số đầu tiên)
            if (in_array($region, [LotteryRegionEnum::MT(), LotteryRegionEnum::MN()]) && ! Arr::get($matchedPrizes, 'zero')) {
                if (in_array(strlen($inputNumber), [5, 6])) {
                    $resultZero = $results->where('level', 'zero')->first();
                    if ($resultZero && substr($inputNumber, 1) == substr($resultZero->number, 1)) {
                        $matchedPrizes['sub-zero'] = [
                            'id' => $resultZero->id,
                            'result' => $resultZero,
                            'prize' => $prizeStructure['sub-zero']['name'],
                            'number' => $resultZero->number,
                            'matched_digits' => substr($inputNumber, 1),
                            'amount' => number_format($prizeStructure['sub-zero']['amount'], 0, ',', '.') . ' đ',
                        ];
                    }
                }
            }
        }

        return $matchedPrizes;
    }

    public function lotoXienStatistic($cityDates, $numbers, $listLotos = [], $type = 2)
    {
        $statistics = [];

        foreach ($cityDates->reverse() as $cityDate) {
            $filtered = $numbers
                ->where('lottery_id', $cityDate->id)
                ->unique('last_two_digits')
                ->values();

            if ($type == 2) {
                // Xiên 2: tổ hợp 2 số
                foreach ($filtered as $i => $number1) {
                    if ($listLotos && ! in_array($number1->last_two_digits, $listLotos)) {
                        continue;
                    }

                    foreach ($filtered->skip($i + 1) as $number2) {
                        if ($number1->last_two_digits == $number2->last_two_digits) {
                            continue;
                        }

                        if ($number1->last_two_digits < $number2->last_two_digits) {
                            $pair = $number1->last_two_digits . ' - ' . $number2->last_two_digits;
                        } else {
                            $pair = $number2->last_two_digits . ' - ' . $number1->last_two_digits;
                        }

                        // Tăng tần suất xuất hiện của cặp số
                        if (! isset($statistics[$pair])) {
                            $statistics[$pair] = [
                                'times' => 0,
                                'dates' => [],
                            ];
                        }

                        $statistics[$pair]['times']++;
                        $statistics[$pair]['dates'][] = $number1->date_formatted;
                    }
                }
            } elseif ($type == 3) {
                // Xiên 3: tìm tổ hợp 3 số cùng xuất hiện trong 1 ngày
                // Tạo tất cả tổ hợp 3 số từ các số đã lọc
                $filteredArray = $filtered->values()->all();
                $count = count($filteredArray);

                for ($i = 0; $i < $count - 2; $i++) {
                    $number1 = $filteredArray[$i];

                    if ($listLotos && ! in_array($number1->last_two_digits, $listLotos)) {
                        continue;
                    }

                    for ($j = $i + 1; $j < $count - 1; $j++) {
                        $number2 = $filteredArray[$j];

                        if ($number1->last_two_digits == $number2->last_two_digits) {
                            continue;
                        }

                        for ($k = $j + 1; $k < $count; $k++) {
                            $number3 = $filteredArray[$k];

                            if ($number1->last_two_digits == $number3->last_two_digits ||
                                $number2->last_two_digits == $number3->last_two_digits) {
                                continue;
                            }

                            // Sắp xếp 3 số theo thứ tự tăng dần
                            $trio = collect([
                                $number1->last_two_digits,
                                $number2->last_two_digits,
                                $number3->last_two_digits,
                            ])->sort()->values();

                            $pair = $trio->implode(' - ');

                            // Tăng tần suất xuất hiện của bộ 3 số
                            if (! isset($statistics[$pair])) {
                                $statistics[$pair] = [
                                    'times' => 0,
                                    'dates' => [],
                                ];
                            }

                            $statistics[$pair]['times']++;
                            $statistics[$pair]['dates'][] = $number1->date_formatted;
                        }
                    }
                }
            }
        }

        // Sort theo số lần xuất hiện (giảm dần), sau đó theo key (tăng dần)
        $statistics = collect($statistics)
            ->sortBy(fn ($item, $key) => $key)
            ->sortByDesc('times');

        return $statistics;
    }

    public function twoPointUniqueVipMB($city, $numbers, $type)
    {
        $result = $this->twoPointUniqueVipMBWithCarry($city, $numbers, $type);

        return [
            $result['found'],
            $result['numbersGrouped'],
            $result['combinations'],
        ];
    }

    public function twoPointUniqueVipMBWithCarry($city, $numbers, $type, array $carryData = []): array
    {
        $numbersGrouped = $this->getNumbersGrouped($numbers, $city->region);

        $combinations = $numbersGrouped
            ->map(function ($group) {
                $values = $group->pluck('splits')->collapseWithKeys()->toArray();

                // Gọi hàm
                $combinations = $this->combinationsOfTwoWithKeys($values);

                return $combinations;
            });

        $combination = $combinations->first();

        $found = [];
        $carryOver = [];

        foreach ($combination as $keys => $values) {
            $iii = 0;
            // Khởi tạo streak từ carry của tháng trước (nếu có)
            $iVip = $carryData[$keys]['streak'] ?? 0;
            $carryCount = $carryData[$keys]['current_count'] ?? 0;
            $carryCountx = $carryData[$keys]['current_countx'] ?? 0;
            $streakWasCarried = $iVip > 0; // Đánh dấu streak được kế thừa từ tháng trước

            foreach ($numbersGrouped->skip(1) as $date => $items) {
                $currentCombination = $combinations->skip($iii)->first();

                if (! isset($currentCombination[$keys])) {
                    $iii++;

                    continue;
                }

                $cvaluex = $currentCombination[$keys];

                $cvalue = str_replace('-', '', $cvaluex);

                $abcd = [
                    $cvalue,
                    strrev($cvalue),
                ];

                $xxcount = $items->whereIn('last_two_digits', $abcd)->count();
                if ($xxcount >= 1) {
                    $iVip += 1;
                } else {
                    if ($iVip) {
                        $found[$keys][$iVip] ??= [
                            'count' => 0,
                            'countx' => 0,
                        ];

                        // Nếu đây là lần đứt đầu tiên và có carry từ tháng trước
                        if ($streakWasCarried && $carryCount > 0) {
                            $found[$keys][$iVip]['count'] += $carryCount;
                            $found[$keys][$iVip]['countx'] += $carryCountx;
                            $carryCount = 0; // Reset để không cộng lại
                            $carryCountx = 0;
                        }

                        $found[$keys][$iVip]['count'] += 1;
                        $found[$keys][$iVip]['countx'] += $xxcount;
                    }
                    $iVip = 0;
                    $streakWasCarried = false; // Reset flag sau khi streak bị đứt
                }
                $iii++;
            }

            // Nếu vẫn còn streak đang chạy khi kết thúc vòng lặp, ghi nhận nó
            if ($iVip > 0) {
                $found[$keys][$iVip] ??= [
                    'count' => 0,
                    'countx' => 0,
                ];

                // Nếu đây là streak được kế thừa từ tháng trước và chưa bị đứt
                if ($streakWasCarried && $carryCount > 0) {
                    $found[$keys][$iVip]['count'] += $carryCount;
                    $found[$keys][$iVip]['countx'] += $carryCountx;
                    $carryCount = 0;
                    $carryCountx = 0;
                }

                // Ghi nhận streak hiện tại
                $found[$keys][$iVip]['count'] += 1;
                // countx không tăng ở đây vì đây là streak vẫn đang chạy
            }

            // Tính toán count hiện tại để lưu vào carry
            $currentCount = 0;
            $currentCountx = 0;

            if ($iVip > 0) {
                // Nếu streak vẫn tiếp tục đến cuối tháng
                if ($streakWasCarried && $carryCount > 0) {
                    // Vẫn còn kế thừa từ tháng trước và chưa bị đứt
                    $currentCount = $carryCount;
                    $currentCountx = $carryCountx;
                } else {
                    // Streak mới hoặc đã đứt và bắt đầu lại
                    $currentCount = $found[$keys][$iVip]['count'] ?? 0;
                    $currentCountx = $found[$keys][$iVip]['countx'] ?? 0;
                }
            }

            $carryOver[$keys] = [
                'streak' => $iVip,
                'current_count' => $currentCount,
                'current_countx' => $currentCountx,
            ];
        }

        return [
            'found' => $found,
            'numbersGrouped' => $numbersGrouped,
            'combinations' => $combinations,
            'carry' => $carryOver,
        ];
    }

    public function twoPointTodayVipMB($city, $numbers, $type)
    {
        $result = $this->twoPointTodayVipMBWithCarry($city, $numbers, $type);

        return [
            $result['found'],
            $result['numbersGrouped'],
            $result['combinations'],
        ];
    }

    public function twoPointTodayVipMBWithCarry($city, $numbers, $type = 'two-point'): array
    {
        $numbersGrouped = $this->getNumbersGrouped($numbers, $city->region, 'latest');

        $combinations = $numbersGrouped
            ->map(function ($group) {
                $values = $group->pluck('splits')->collapseWithKeys()->toArray();

                // Gọi hàm
                $combinations = $this->combinationsOfTwoWithKeys($values);

                return $combinations;
            });

        $combination = $combinations->first();

        $found = [];

        foreach ($combination as $keys => $values) {
            $iii = 0;
            $iVip = [];

            foreach ($numbersGrouped->skip(1) as $date => $items) {
                $currentCombination = $combinations->skip($iii + 1)->first();
                // lấy combination ngày tiếp theo

                if (! isset($currentCombination[$keys])) {
                    $iii++;

                    continue;
                }

                $cvaluex = $currentCombination[$keys];

                $cvalue = str_replace('-', '', $cvaluex);

                $abcd = [
                    $cvalue,
                    strrev($cvalue),
                ];

                $preItems = $numbersGrouped->skip($iii)->first();

                $lastTwoDigits = $preItems->whereIn('last_two_digits', $abcd);
                $xxcount = $lastTwoDigits->count();
                if ($xxcount >= 1) {
                    $iVip = [
                        ...$iVip,
                        ...$lastTwoDigits->map(fn ($item) => ['number' => $item->number, 'date' => $item->date])->toArray(),
                    ];
                } else {
                    if ($iVip) {
                        $found[$keys][$iii] ??= [
                            'count' => 0,
                            'countx' => 0,
                            'numbers' => [],
                        ];

                        $found[$keys][$iii]['count'] += $iii;
                        $found[$keys][$iii]['countx'] += $xxcount;
                        $found[$keys][$iii]['numbers'] += $iVip;
                    }

                    break;
                }
                $iii++;
            }

            if ($iii) {
                $found[$keys][$iii] ??= [
                    'count' => 0,
                    'countx' => 0,
                    'numbers' => [],
                ];
                $found[$keys][$iii]['count'] += 1;
                $found[$keys][$iii]['countx'] += 0;
                $found[$keys][$iii]['numbers'] += $iVip;
            }
        }

        return [
            'found' => $found,
            'numbersGrouped' => $numbersGrouped,
            'combinations' => $combinations,
        ];
    }

    public function threePointTodayVipMB($city, $numbers, $type)
    {
        $result = $this->threePointTodayVipMBWithCarry($city, $numbers, $type);

        return [
            $result['found'],
            $result['numbersGrouped'],
            $result['combinations'],
        ];
    }

    public function threePointTodayVipMBWithCarry($city, $numbers, $type = 'three-point'): array
    {
        // Tăng memory limit tạm thời
        $originalMemoryLimit = ini_get('memory_limit');
        ini_set('memory_limit', '1024M');

        try {
            $numbersGrouped = $this->getNumbersGrouped($numbers, $city->region, 'latest');

            // Giới hạn số lượng ngày để tránh memory exhausted
            if ($numbersGrouped->count() > 60) {
                $numbersGrouped = $numbersGrouped->take(60);
            }

            $combinations = $numbersGrouped
                ->map(function ($group) {
                    $values = $group->pluck('splits')->collapseWithKeys()->toArray();

                    // Gọi hàm
                    return $this->combinationsOfThreeWithKeys($values);
                });

            dd(count($combinations));
            $combination = $combinations->first();

            // Giới hạn số lượng combinations để xử lý
            if (count($combination) > 1000) {
                $combination = array_slice($combination, 0, 1000, true);
            }

            $found = [];

            foreach ($combination as $keys => $values) {
                $iii = 0;
                $iVip = [];

                foreach ($numbersGrouped->skip(1) as $date => $items) {
                    $currentCombination = $combinations->skip($iii + 1)->first();
                    // lấy combination ngày tiếp theo

                    if (! isset($currentCombination[$keys])) {
                        $iii++;
                        continue;
                    }

                    $cvaluex = $currentCombination[$keys];
                    $cvalue = str_replace('-', '', $cvaluex);

                    $preItems = $numbersGrouped->skip($iii)->first();

                    $lastTwoDigits = $preItems->whereIn('last_two_digits', $cvalue);
                    $xxcount = $lastTwoDigits->count();
                    if ($xxcount >= 1) {
                        $iVip = [
                            ...$iVip,
                            ...$lastTwoDigits->map(fn ($item) => ['number' => $item->number, 'date' => $item->date])->toArray(),
                        ];
                    } else {
                        if ($iVip) {
                            $found[$keys][$iii] ??= [
                                'count' => 0,
                                'countx' => 0,
                                'numbers' => [],
                            ];

                            $found[$keys][$iii]['count'] += $iii;
                            $found[$keys][$iii]['countx'] += $xxcount;
                            $found[$keys][$iii]['numbers'] += $iVip;
                        }

                        break;
                    }
                    $iii++;
                }

                if ($iii) {
                    $found[$keys][$iii] ??= [
                        'count' => 0,
                        'countx' => 0,
                        'numbers' => [],
                    ];
                    $found[$keys][$iii]['count'] += 1;
                    $found[$keys][$iii]['countx'] += 0;
                    $found[$keys][$iii]['numbers'] += $iVip;
                }

                // Giải phóng memory định kỳ
                if (count($found) % 100 === 0) {
                    gc_collect_cycles();
                }
            }

            return [
                'found' => $found,
                'numbersGrouped' => $numbersGrouped,
                'combinations' => $combinations,
            ];

        } finally {
            // Khôi phục memory limit ban đầu
            ini_set('memory_limit', $originalMemoryLimit);
        }
    }

    public function getNumbersGrouped($numbers, $region, $orderBy = 'oldest')
    {
        $prizeKeys = array_keys(LotterySupport::getPrizeEnums($region));

        return $numbers
            ->when($orderBy === 'oldest', fn ($query) => $query->sortBy('date'), fn ($query) => $query->sortByDesc('date'))
            ->groupBy('date')
            ->map(function ($items) use ($region, $prizeKeys) {
                $i = 1;
                $items = $items
                    ->sortBy(function ($item) use ($prizeKeys) {
                        return array_search($item->level, $prizeKeys);
                    })
                    ->values()
                    ->map(function ($item) use (&$i) {
                        $splits = [];
                        foreach (str_split($item->number) as $value) {
                            $splits[$i++] = $value;
                        }

                        $item->splits = $splits;

                        return $item;
                    });

                return $items;
            });
    }

    public function combinationsOfTwoWithKeys(array $array): array
    {
        $result = [];
        $keys = array_keys($array);
        $count = count($keys);

        for ($i = 0; $i < $count - 1; $i++) {
            for ($j = $i + 1; $j < $count; $j++) {
                $key1 = $keys[$i];
                $key2 = $keys[$j];

                $val1 = $array[$key1];
                $val2 = $array[$key2];

                $result[implode('-', [$key1, $key2])] = implode('-', [$val1, $val2]);
            }
        }

        return $result;
    }

    public function combinationsOfThreeWithKeys(array $array): array
    {
        $result = [];
        $keys = array_keys($array);
        $count = count($keys);
        // dd($count);
        // Giới hạn số lượng keys để tránh memory exhausted
        // Với 27 số (thường là số lượng chữ số trong kết quả xổ số MB)
        // 27C3 = 2925 tổ hợp, vẫn có thể quản lý được
        // if ($count > 30) {
        //     // Nếu quá nhiều, chỉ lấy 30 keys đầu tiên
        //     $keys = array_slice($keys, 0, 30);
        //     $count = 30;
        // }

        for ($i = 0; $i < $count - 2; $i++) {
            for ($j = $i + 1; $j < $count - 1; $j++) {
                for ($k = $j + 1; $k < $count; $k++) {
                    $key1 = $keys[$i];
                    $key2 = $keys[$j];
                    $key3 = $keys[$k];

                    $val1 = $array[$key1];
                    $val2 = $array[$key2];
                    $val3 = $array[$key3];

                    $result[implode('-', [$key1, $key2, $key3])] = implode('-', [$val1, $val2, $val3]);

                    // Giải phóng memory định kỳ
                    if (count($result) % 1000 === 0) {
                        gc_collect_cycles();
                    }
                }
            }
        }

        return $result;
    }

    public function getNumbersForRange(LotteryCity $city, Carbon $startDate, Carbon $endDate, $orderBy = 'oldest', $type = 'two')
    {
        if ($endDate->lt($startDate)) {
            return collect();
        }

        return LotteryRegionEnum::getModelQuery($city->region)
            ->select(['*'])
            ->addSelect([
                $type == 'three' ? DB::raw('RIGHT(number, 3) AS last_three_digits') : DB::raw('RIGHT(number, 2) AS last_two_digits'),
                DB::raw('DATE_FORMAT(date, \'%d-%m-%Y\') AS date_formatted'),
            ])
            ->where('city_id', $city->getKey())
            ->whereBetween('date', [$startDate->toDateString(), $endDate->toDateString()])
            ->when($orderBy === 'oldest', fn ($query) => $query->oldest('date'), fn ($query) => $query->latest('date')->orderBy('id'))
            ->getQuery()
            ->get();
    }

    public function statisticSpecial($city, $numbers, $type)
    {
        $result = $this->statisticSpecialWithCarry($city, $numbers, $type);

        return [
            $result['found'],
            $result['numbersGrouped'],
            $result['combinations'],
        ];
    }

    public function statisticSpecialWithCarry($city, $numbers, $type = 'first'): array
    {
        $region = $city->region;

        $numbersGrouped = $this->getNumbersGrouped($numbers, $city->region, 'latest');

        $combinations = $numbersGrouped
            ->map(function ($group) {
                $values = $group->pluck('splits')->collapseWithKeys()->toArray();

                // Gọi hàm
                $combinations = $this->combinationsOfTwoWithKeys($values);

                return $combinations;
            });

        $combination = $combinations->first();

        $found = [];

        switch ($region) {
            case LotteryRegionEnum::MT:
            case LotteryRegionEnum::MN:
                $xkeys = match ($type) {
                    'last', 'total-last' => '81-82',
                    'giai-8' => '1-2',
                    default => '77-78',
                };
                break;
            default:
                $xkeys = match ($type) {
                    'last', 'total-last' => '4-5',
                    default => '1-2',
                };
                break;
        };

        $xtype = match ($type) {
            'total-last', 'total-last' => 'total',
            'first', 'last' => 'default',
            default => 'default',
        };

        foreach ($combination as $keys => $values) {
            $iii = 0;
            $iVip = [];

            foreach ($numbersGrouped->skip($iii + 1) as $date => $items) {
                $beforeCombination = $combinations->skip($iii)->first();
                $currentCombination = $combinations->skip($iii + 1)->first();

                if (! isset($beforeCombination[$keys])) {
                    $iii++;

                    continue;
                }

                $cvaluex = $beforeCombination[$xkeys];

                $cvalue = str_replace('-', '', $cvaluex);

                $xtrue = false;
                if ($xtype == 'default') {
                    $abcd = [
                        $cvalue,
                        strrev($cvalue),
                    ];
                    
                    $xvalue = str_replace('-', '', $currentCombination[$keys]);

                    $xtrue = in_array($xvalue, $abcd);
                } elseif ($xtype == 'total') {
                    $sum = array_sum(str_split($cvalue));

                    $xvalue = array_sum(str_split(str_replace('-', '', $currentCombination[$keys])));

                    $xtrue = intval($xvalue) === $sum;
                }

                $xxcount = 1;

                if ($xtrue) {
                    $iVip = [
                        ...$iVip,
                        ...[$xvalue],
                    ];
                } else {
                    if ($iVip) {
                        $found[$keys][$iii] ??= [
                            'count' => 0,
                            'countx' => 0,
                            'numbers' => [],
                        ];

                        $found[$keys][$iii]['count'] += $iii;
                        $found[$keys][$iii]['countx'] += $xxcount;
                        $found[$keys][$iii]['numbers'] += $iVip;
                    }

                    break;
                }
                $iii++;
            }

            if ($iii) {
                $found[$keys][$iii] ??= [
                    'count' => 0,
                    'countx' => 0,
                    'numbers' => [],
                ];
                $found[$keys][$iii]['count'] += 1;
                $found[$keys][$iii]['countx'] += 0;
                $found[$keys][$iii]['numbers'] += $iVip;
            }
        }

        // dd($found);

        return [
            'found' => $found,
            'numbersGrouped' => $numbersGrouped,
            'combinations' => $combinations,
        ];
    }
}
