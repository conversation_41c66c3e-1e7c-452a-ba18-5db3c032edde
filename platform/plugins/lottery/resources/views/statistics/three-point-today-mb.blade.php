@use(Carbon\Carbon)
@use(Plugin\Lottery\Facades\LotterySupport)
@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <style>
        th.sortable { cursor: pointer; position: relative; user-select:none; }
        th.sortable::after {
            content: ""; position: absolute; right: 3px; top: 50%; transform: translateY(-50%);
            font-size: 12px;
        }
        th.sortable.sorted-asc::after  { content: "▲"; }
        th.sortable.sorted-desc::after { content: "▼"; }
        th.sortable.sorted-none::after { content: "↕"; opacity: .35; } /* trạng thái mặc định có thể hiện mũi tên nhạt */


        .table-2-points .fs-4 b {
            font-size: 1.32rem !important;
        }

        /* overlay */
        #tableLoading {
            display: none;
            position: absolute;
            top: 0; left: 0; right: 0; bottom: 0;
            background: rgba(255,255,255,0.8);
            justify-content: center;
            align-items: center;
            font-weight: bold;
            z-index: 10;
        }
            #tableLoading .spinner {
            border: 4px solid #eee;
            border-top: 4px solid #333;
            border-radius: 50%;
            width: 20px; height: 20px;
            margin-right: 8px;
            animation: spin 1s linear infinite;
            display:inline-block;
        }
        @keyframes spin { 100% { transform: rotate(360deg); } }

        .cursor-pointer { cursor: pointer; }
    </style>

    <div class="mb-3">
        <div class="row align-items-center">
            @if(isset($cities) && $cities->count() > 1)
                <div class="col-auto">
                    <label class="form-label mb-0">Thành phố:</label>
                </div>
                <div class="col-auto">
                    <select name="city_id" id="citySelect" class="form-select">
                        @foreach ($cities as $xcity)
                            <option value="{{ $xcity->id }}" {{ $xcity->id == $city->id ? 'selected' : '' }}>
                                {{ $xcity->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
            @endif

            <div class="col-auto">
                <label class="form-label mb-0">Dòng/trang:</label>
            </div>
            <div class="col-auto">
                <select name="per_page" id="perPageSelect" class="form-select">
                    <option value="25" {{ request('per_page', 50) == 25 ? 'selected' : '' }}>25</option>
                    <option value="50" {{ request('per_page', 50) == 50 ? 'selected' : '' }}>50</option>
                    <option value="100" {{ request('per_page', 50) == 100 ? 'selected' : '' }}>100</option>
                    <option value="200" {{ request('per_page', 50) == 200 ? 'selected' : '' }}>200</option>
                    <option value="500" {{ request('per_page', 50) == 500 ? 'selected' : '' }}>500</option>
                </select>
            </div>
            @if (isset($pagination))
                <div class="col-auto">
                    <span class="badge bg-secondary">
                        <i class="fa fa-list"></i> {{ number_format($pagination['total']) }} dòng
                    </span>
                </div>
            @endif
        </div>
    </div>
    
    <script>
        // City select reload page with city_id parameter
        const citySelect = document.getElementById('citySelect');
        if (citySelect) {
            citySelect.addEventListener('change', function() {
                const url = new URL(window.location.href);
                url.searchParams.set('city_id', this.value);
                url.searchParams.delete('page');
                window.location.href = url.toString();
            });
        }

        // Per page select không reload, chỉ render lại
        document.getElementById('perPageSelect').addEventListener('change', function() {
            if (window.tablePagination) {
                window.tablePagination.perPage = parseInt(this.value);
                window.tablePagination.currentPage = 1;
                window.tablePagination.render();
            }
        });
    </script>

    <div class="block data-append-form">
        <div class="block-main-heading">
            <h2 class="fs-5 mb-0">Chi tiết thống kê {{ PageTitle::getTitle(false) }}</h2>
        </div>
        @php
            $quantity = collect($found)->max(fn ($items) => max(array_keys($items))) ?? 10;
            $dateNumbers = $numbers->groupBy('date');
            $firstNumbers = $numbersGrouped->first() ?? collect();
            $lastNumbers = $numbersGrouped->last() ?? $firstNumbers;
            $numberQuantity = $firstNumbers->isNotEmpty() ? $firstNumbers->sum(fn ($item) => strlen($item->number ?? '')) : 27;
            
            // Tạo mapping ID -> Số từ bảng hiển thị
            $idToNumber = [];
            $idx = 0;
            foreach ($firstNumbers as $item) {
                foreach ($item->splits ?? [] as $num) {
                    $idx++;
                    $idToNumber[$idx] = $num;
                }
            }
        @endphp
        
        {{-- Đưa toàn bộ data vào JS --}}
        <script>
            window.tableData = {
                found: @json($found),
                idToNumber: @json($idToNumber),
                quantity: {{ $quantity }},
                numberQuantity: {{ $numberQuantity }}
            };
        </script>
        
        <div class="block-main-content position-relative">
            <table id="statTable" class="table table-bordered table-striped table-2-points">
                <thead>
                    <tr>
                        <th rowspan="2">
                            <span>ID</span>
                            <div>
                                <select name="id_number_1">
                                    <option value=""></option>
                                    @for ($i = 1; $i < $numberQuantity; $i++)
                                        <option value="{{ $i }}">{{ $i }}</option>
                                    @endfor
                                </select>
                                <select name="id_number_2">
                                    <option value=""></option>
                                    @for ($i = 2; $i <= $numberQuantity; $i++)
                                        <option value="{{ $i }}">{{ $i }}</option>
                                    @endfor
                                </select>
                            </div>
                        </th>
                        <th rowspan="2">
                            <span>Số tương ứng</span>
                        </th>
                        <th rowspan="2" class="sortable" data-col="total">
                            <span>Số lần</span>
                        </th>
                    </tr>
                </thead>
                <tbody id="tableBody">
                    {{-- Render bằng JS --}}
                </tbody>
            </table>

            <!-- overlay loading -->
            <div id="tableLoading">
                <div class="spinner"></div> Đang tải...
            </div>
        </div>

        {{-- Pagination render by JS --}}
        <div class="mt-3" id="paginationContainer"></div>

        @php
            $items = $firstNumbers;
        @endphp
        
        @if ($firstNumbers->isNotEmpty())
            <div class="block-main-content">
                <table class="table table-bordered table-striped table-2-points">
                    <tbody>
                        @php
                            $iii = 0;
                        @endphp
                        @foreach (LotterySupport::getPrizeEnums((string) $city->region) as $prizeKey => $prizeLabel)
                            @php
                                $prizes = $items->where('level', $prizeKey);
                            @endphp
                            <tr>
                                <td>{{ $prizeLabel }}</td>
                                <td class="text-center">
                                    @php
                                        $levels = $items->where('level', $prizeKey);
                                    @endphp
                                    <div
                                        @class([
                                            'row justify-content-center',
                                            'row-cols-3' => $levels->count() > 5,
                                        ])>
                                        @foreach ($levels as $item)
                                            <div
                                                data-index="{{ $loop->index }}"
                                                class="col fs-4 fw-bold prize-{{ $prizeKey }} 2-point-prize"
                                                data-last_two_digits="{{ $item->last_two_digits }}">
                                                <div class="d-flex justify-content-center">
                                                    @foreach ($item->splits as $k => $num)
                                                        <b title="{{ $city->region . '-' . ++$iii . '-' . $item->date}}"
                                                            data-key="{{ $iii }}"
                                                            data-num="{{ $num }}"
                                                            style="cursor: pointer"
                                                            @class([
                                                                'tkcault',
                                                                'setlotocolor',
                                                                'last_two_digits' => $loop->last || $loop->index === $loop->count - 2
                                                            ])>{{ $num }}</b>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endif
    </div>
@endsection

@push('footer')
    <script>
        // Client-side Pagination Manager
        class TablePagination {
            constructor(data, idToNumber, quantity) {
                console.log(data);
                this.allData = Object.entries(data); // Convert object to array of [key, value]
                this.filteredData = [...this.allData];
                this.idToNumber = idToNumber;
                this.quantity = quantity;
                this.currentPage = 1;
                this.perPage = parseInt(document.getElementById('perPageSelect')?.value) || 50;
                this.sortState = { col: null, dir: 'none' };
                this.filterState = { num1: '', num2: '' };
            }

            get totalItems() {
                return this.filteredData.length;
            }

            get totalPages() {
                return Math.ceil(this.totalItems / this.perPage);
            }

            get pageData() {
                const start = (this.currentPage - 1) * this.perPage;
                const end = start + this.perPage;
                return this.filteredData.slice(start, end);
            }

            applyFilter(num1, num2) {
                this.filterState = { num1, num2 };
                
                if (!num1 && !num2) {
                    this.filteredData = [...this.allData];
                } else {
                    this.filteredData = this.allData.filter(([xkey]) => {
                        const keys = xkey.split('-');
                        if (num1 && num2) {
                            return keys[0] == num1 && keys[1] == num2;
                        } else if (num1) {
                            return keys[0] == num1;
                        } else {
                            return keys[1] == num2;
                        }
                    });
                }
                
                this.currentPage = 1;
                this.render();
            }

            calculateTotal(items) {
                // let total = 0;
                // for (let i = 1; i <= this.quantity; i++) {
                //     const count = items[i]?.count || 0;
                //     total += count * i;
                // }
                // return total;

                return Object.keys(items)[0];
            }

            calculateRate(items) {
                let sum = 0;
                for (let i = 1; i <= this.quantity; i++) {
                    sum += items[i]?.count || 0;
                }
                const total = this.calculateTotal(items);
                if (total === 0) return 0;
                return 1 - (sum / total);
            }

            applySort(colIdx, dir) {
                this.sortState = { col: colIdx, dir };
                
                if (dir === 'none') {
                    // Reset to original order
                    this.filteredData.sort((a, b) => {
                        return this.allData.indexOf(a) - this.allData.indexOf(b);
                    });
                } else {
                    this.filteredData.sort((a, b) => {
                        let valA, valB;
                        
                        if (colIdx === 'total') {
                            valA = this.calculateTotal(a[1]);
                            valB = this.calculateTotal(b[1]);
                        } else if (colIdx === 'rate') {
                            valA = this.calculateRate(a[1]);
                            valB = this.calculateRate(b[1]);
                        } else {
                            valA = a[1][colIdx - 1]?.count || 0;
                            valB = b[1][colIdx - 1]?.count || 0;
                        }
                        
                        return dir === 'asc' ? valA - valB : valB - valA;
                    });
                }
                
                this.render();
            }

            renderRows() {
                const tbody = document.getElementById('tableBody');
                const fragment = document.createDocumentFragment();
                
                this.pageData.forEach(([xkey, items]) => {
                    const keys = xkey.split('-');
                    const num1 = this.idToNumber[keys[0]] || '';
                    const num2 = this.idToNumber[keys[1]] || '';
                    const num3 = this.idToNumber[keys[2]] || '';
                    
                    const tr = document.createElement('tr');
                    tr.dataset.number1 = keys[0] || '';
                    tr.dataset.number2 = keys[1] || '';
                    tr.dataset.number3 = keys[2] || '';
                    tr.dataset.initialIndex = this.allData.findIndex(item => item[0] === xkey);
                    
                    let html = `
                        <td class="fw-bold text-center cursor-pointer set-colortoloto">
                            ${keys.join(' & ')}
                        </td>
                        <td class="fw-bold text-center">${num1} & ${num2} & ${num3}</td>
                    `;
                    
                    // Calculate and add Total column
                    const total = this.calculateTotal(items);
                    const firstEntry = Object.values(items)[0];

                    html += `<td class="fw-bold text-center" title="${firstEntry.numbers.map(u => u.date).join(', ')}">${total}</td>`;

                    // Calculate and add Rate column
                    // const rate = this.calculateRate(items);
                    // html += `<td class="fw-bold text-center">${rate.toFixed(4)}</td>`;
                    
                    // for (let i = 1; i <= this.quantity; i++) {
                    //     html += `<th>${items[i]?.count || ''}</th>`;
                    // }
                    
                    tr.innerHTML = html;
                    fragment.appendChild(tr);
                });
                
                tbody.innerHTML = '';
                tbody.appendChild(fragment);
            }

            renderPagination() {
                const container = document.getElementById('paginationContainer');
                
                if (this.totalPages <= 1) {
                    container.innerHTML = '';
                    return;
                }
                
                const from = (this.currentPage - 1) * this.perPage + 1;
                const to = Math.min(this.currentPage * this.perPage, this.totalItems);
                
                let html = `
                    <div class="row align-items-center mb-3">
                        <div class="col">
                            <span class="text-muted">
                                Hiển thị ${from.toLocaleString()} - ${to.toLocaleString()} 
                                trong tổng số ${this.totalItems.toLocaleString()} dòng
                            </span>
                        </div>
                        <div class="col  col-12 col-md-auto">
                            <nav class="ms-2">
                                <ul class="pagination pagination-sm mb-0 justify-content-end">
                `;
                
                // Previous button
                html += `
                    <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${this.currentPage - 1}">&laquo;</a>
                    </li>
                `;
                
                // Page numbers
                for (let i = 1; i <= this.totalPages; i++) {
                    if (i === 1 || i === this.totalPages || Math.abs(i - this.currentPage) <= 2) {
                        html += `
                            <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                                <a class="page-link" href="#" data-page="${i}">${i}</a>
                            </li>
                        `;
                    } else if (Math.abs(i - this.currentPage) === 3) {
                        html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
                    }
                }
                
                // Next button
                html += `
                    <li class="page-item ${this.currentPage === this.totalPages ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${this.currentPage + 1}">&raquo;</a>
                    </li>
                `;
                
                html += `
                                </ul>
                            </nav>
                        </div>
                    </div>
                `;
                
                container.innerHTML = html;
                
                // Attach click events
                container.querySelectorAll('a.page-link').forEach(link => {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        const page = parseInt(link.dataset.page);
                        if (page > 0 && page <= this.totalPages) {
                            this.currentPage = page;
                            this.render();
                            document.getElementById('statTable').scrollIntoView({ behavior: 'smooth' });
                        }
                    });
                });
            }

            render() {
                const loading = document.getElementById('tableLoading');
                loading.style.display = 'flex';
                
                // Use setTimeout to allow browser to render loading indicator
                setTimeout(() => {
                    this.renderRows();
                    this.renderPagination();
                    loading.style.display = 'none';
                }, 10);
            }
        }

        // Initialize on page load
        let tablePagination;
        document.addEventListener('DOMContentLoaded', function() {
            if (window.tableData) {
                tablePagination = new TablePagination(
                    window.tableData.found,
                    window.tableData.idToNumber,
                    window.tableData.quantity
                );
                window.tablePagination = tablePagination;
                tablePagination.render();
            }
        });
    </script>

    <script>
        $(document).ready(function () {
            $('select[name="id_number_1"], select[name="id_number_2"]').on('change', function () {
                if (window.tablePagination) {
                    const number1 = $('select[name="id_number_1"]').val();
                    const number2 = $('select[name="id_number_2"]').val();
                    window.tablePagination.applyFilter(number1, number2);
                }
            });
        });
    </script>

    <script>
        (function () {
            const headers = document.querySelectorAll('th.sortable');
            let activeCol = null;
            let activeState = 'none';

            function setHeaderState(th, state) {
                th.classList.remove('sorted-asc', 'sorted-desc', 'sorted-none');
                th.removeAttribute('aria-sort');
                if (state === 'asc')  { th.classList.add('sorted-asc');  th.setAttribute('aria-sort', 'ascending'); }
                else if (state === 'desc') { th.classList.add('sorted-desc'); th.setAttribute('aria-sort', 'descending'); }
                else { th.classList.add('sorted-none'); th.setAttribute('aria-sort', 'none'); }
            }

            function resetAllHeaders() {
                headers.forEach(h => setHeaderState(h, 'none'));
            }

            function nextState(state) {
                if (state === 'none') return 'desc';
                if (state === 'desc')  return 'asc';
                return 'none';
            }

            headers.forEach(th => {
                setHeaderState(th, 'none');

                th.addEventListener('click', () => {
                    const col = th.dataset.col === 'total' || th.dataset.col === 'rate' 
                        ? th.dataset.col 
                        : parseInt(th.dataset.col, 10);

                    if (activeCol !== col) {
                        resetAllHeaders();
                        activeCol = col;
                        activeState = 'none';
                    }

                    activeState = nextState(activeState);
                    resetAllHeaders();
                    setHeaderState(th, activeState);

                    if (window.tablePagination) {
                        window.tablePagination.applySort(col, activeState);
                    }

                    if (activeState === 'none') activeCol = null;
                });
            });
        })();
    </script>

    <script>
        $(document).ready(function () {
            $(document).on('click', '.set-colortoloto', function () {
                $('.set-colortoloto').removeClass('text-danger');
                $('.setlotocolor').removeClass('text-danger');

                let $e = $(this);
                let $parent = $e.closest('tr');
                let num1 = $parent.data('number1');
                let num2 = $parent.data('number2');

                if (num1 && num2) {
                    $e.addClass('text-danger');

                    $('.setlotocolor[data-key=' + num1 + ']').addClass('text-danger');
                    $('.setlotocolor[data-key=' + num2 + ']').addClass('text-danger');

                    // Scroll to the bottom table smoothly
                    const $targetTable = $('.setlotocolor').first().closest('table');
                    if ($targetTable.length) {
                        $targetTable[0].scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                }
            });
        });
    </script>
@endpush
