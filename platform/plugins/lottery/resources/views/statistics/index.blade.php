@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row row-cols-4">
        <div class="col mb-3">
            <div class="card">
                <div class="card-body">
                    <a href="{{ route('lotteries.statistics.two-point-unique-mb') }}">2 điểm độc nhất miền bắc</a>
                </div>
            </div>
        </div>
        
        <div class="col mb-3">
            <div class="card">
                <div class="card-body">
                    <a href="{{ route('lotteries.statistics.two-point-today-mb') }}">Cầu chạy hôm nay</a>
                </div>
            </div>
        </div>

        {{-- <div class="col mb-3">
            <div class="card">
                <div class="card-body">
                    <a href="{{ route('lotteries.statistics.three-point-today-mb') }}">Thống kê cầu 3 số hôm nay</a>
                </div>
            </div>
        </div> --}}

        @foreach (['mb', 'mt', 'mn'] as $region)
            @foreach (['dau', 'duoi', 'tong-dau', 'tong-duoi'] as $type)
                @php
                    $typeTitle = match ($type) {
                        'duoi' => 'đuôi',
                        'tong-dau' => 'tổng đầu',
                        'tong-duoi' => 'tổng đuôi',
                        default => 'đầu',
                    };
                @endphp
                <div class="col mb-3">
                    <div class="card">
                        <div class="card-body">
                            <a href="{{ route('lotteries.statistics.special', ['type' => $type, 'region' => $region]) }}">Cầu đặc biệt {{ $typeTitle }} {{ Str::upper($region) }}</a>
                        </div>
                    </div>
                </div>
            @endforeach

            @if ($region != 'mb')
                <div class="col mb-3">
                    <div class="card">
                        <div class="card-body">
                            <a href="{{ route('lotteries.statistics.special', ['type' => 'giai-8', 'region' => $region]) }}">Cầu giải 8 {{ Str::upper($region) }}</a>
                        </div>
                    </div>
                </div>
            @endif
        @endforeach
    </div>
    
@endsection
